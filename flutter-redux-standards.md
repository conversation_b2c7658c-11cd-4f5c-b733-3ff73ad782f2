# Flutter - Redux使用规范

## 目标

- 明确各模块之间职责边界
- 影响State变化的副作用都集中在Middleware，方便核心业务梳理
- state使用copyWith修改，可确保reducer有稳定的I/O，以引入单元测试

## 整体架构关系图

```mermaid
flowchart LR
    %% SmartHome根视图在最左侧
    SmartHome[🏠 SmartHome<br/>根视图生命周期]
    LifecycleActions[📋 生命周期Action<br/>initState/didAppear/didDisappear]

    %% UI层居中
    UI[🖥️ UI 组件<br/>Flutter Widgets]
    WidgetUtils[🔧 WidgetUtils<br/>UI工具类]
    Selectors[📊 Selectors<br/>数据选择器]

    %% Redux核心组件和Middleware在右侧
    StoreProvider[🏗️ StoreProvider<br/>Store注入]
    StoreConnector[🔌 StoreConnector<br/>UI-Store桥梁]
    Dispatcher[🚀 Dispatcher<br/>Store单例管理]
    Store[🏪 Redux Store<br/>单一状态树]
    State[📊 State<br/>应用状态数据]
    Action[📝 Action<br/>事件描述]
    Reducer[⚙️ Reducer<br/>纯函数状态更新]

    AuthMiddleware[🔐 AuthMiddleware<br/>认证模块副作用]
    DeviceMiddleware[📱 DeviceMiddleware<br/>设备模块副作用]
    FamilyMiddleware[👨‍👩‍👧‍👦 FamilyMiddleware<br/>家庭模块副作用]

    %% 逻辑流向
    SmartHome -->|生命周期事件| LifecycleActions
    LifecycleActions -->|dispatch| Dispatcher
    UI -.->|用户交互| Action
    Action -->|dispatch| Dispatcher
    Dispatcher -->|转发| Store
    Store -->|拦截| AuthMiddleware
    Store -->|拦截| DeviceMiddleware
    Store -->|拦截| FamilyMiddleware
    AuthMiddleware -.->|dispatch新Action| Dispatcher
    DeviceMiddleware -.->|dispatch新Action| Dispatcher
    FamilyMiddleware -.->|dispatch新Action| Dispatcher
    Store -->|传递Action| Reducer
    Reducer -->|更新| State
    State -->|存储在| Store
    Store -->|状态变化| StoreConnector
    StoreConnector -->|数据选择| Selectors
    Selectors -->|转换数据| UI
    StoreProvider -->|提供Store| StoreConnector
    UI -->|UI操作| WidgetUtils
    WidgetUtils -->|副作用处理| UI

    %% 布局优化
    SmartHome --- UI
    UI --- StoreConnector
    StoreConnector --- Store
    Store --- AuthMiddleware
    Store --- DeviceMiddleware
    Store --- FamilyMiddleware

    %% 样式
    classDef uiClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef reduxClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef middlewareClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef lifecycleClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef utilsClass fill:#fff8e1,stroke:#f57c00,stroke-width:2px

    class SmartHome,LifecycleActions lifecycleClass
    class UI,WidgetUtils,Selectors,StoreConnector,StoreProvider uiClass
    class Store,State,Action,Reducer,Dispatcher reduxClass
    class AuthMiddleware,DeviceMiddleware,FamilyMiddleware middlewareClass
```

## **文件结构**

```text
lib/
├── main.dart                    # 应用入口
├── app.dart                     # 根组件
├── smarthome/                   # 应用级别配置
│   ├── smarthome.dart          # 主页面
│   └── store/                  # Redux相关
│       ├── smarthome_actions.dart
│       ├── smarthome_reducer.dart
│       ├── smarthome_state.dart
│       ├── smarthome_middleware.dart
│       ├── smarthome_dispatcher.dart
│       └── smarthome_state.freezed.dart
├── common/                      # 通用模块
│   ├── constants/              # 常量定义
│   │   └── smarthome_constant.dart
│   ├── utils/                  # 工具方法
│   ├── widgets/                # 通用组件
│   └── services/              # 通用服务（HTTP等）
│       ├── http_service.dart
│       ├── rest_client.dart
│       └── rest_client.g.dart
├── features/                   # 功能模块
│   ├── auth/                  # 认证功能
│   │   └── store/            # Redux相关
│   │       ├── auth_actions.dart
│   │       ├── auth_reducer.dart
│   │       ├── auth_state.dart
│   │       ├── auth_middleware.dart
│   │       └── auth_state.freezed.dart
│   ├── device/                # 设备功能
│   │   ├── store/            # Redux相关
│   │   │   ├── device_actions.dart
│   │   │   ├── device_reducer.dart
│   │   │   ├── device_state.dart
│   │   │   ├── device_middleware.dart
│   │   │   └── device_state.freezed.dart
│   │   ├── widgets/          # UI组件
│   │   │   ├── device_widget.dart
│   │   │   └── device_ui_tools.dart
│   │   ├── utils/            # 工具方法
│   │   ├── constants/        # 常量定义
│   │   ├── viewmodel/        # 视图模型
│   │   ├── services/         # 服务层
│   │   └── selectors/        # 选择器
│   ├── family/               # 家庭功能
│   │   └── store/           # Redux相关
│   │       ├── family_actions.dart
│   │       ├── family_reducer.dart
│   │       ├── family_state.dart
│   │       └── family_state.freezed.dart
│   ├── wholehouse/           # 全屋功能
│   │   ├── store/           # Redux相关
│   │   │   ├── wholehouse_actions.dart
│   │   │   ├── wholehouse_reducer.dart
│   │   │   ├── wholehouse_state.dart
│   │   │   ├── wholehouse_middleware.dart
│   │   │   └── wholehouse_state.freezed.dart
│   │   ├── services/        # 服务层
│   │   │   └── env_devices_response_model.dart
│   │   ├── constants/       # 常量定义
│   │   └── widgets/         # UI组件
│   └── navigator/           # 导航功能
│       ├── selectors/       # 选择器
│       │   └── navigator_selectors.dart
│       └── widgets/         # UI组件
│           └── navigator.dart
└── Mock/                    # Mock数据
    ├── mock_user.dart
    └── mock_family.dart
```

## **State 使用规范**

> **State职责**：存储应用的所有状态数据
应该放：基础数据类型、列表、Map等可序列化的业务数据；
不应该放：函数、UI对象、异步对象、系统资源等不可序列化的值；

### 1. **不要直接修改 State**
>
>修改 state 是 Redux 应用 bug 的最常见的诱因，包括组件没有正确再渲染，且阻碍了 Redux DevTools 的时间旅行调试。无论是 reducer 中还是任意其他应用代码中，都要始终避免 state 的真实变换。

#### ❌ **错误用法 - 在Reducer中直接修改State**

```dart
// lib/device/store/device_reducer.dart:294
SmartHomeState _updateDeviceTabIndex(SmartHomeState state, UpdateDeviceTabIndexAction action) {
  state.deviceState.deviceTabIndex = action.index;  // ❌ 直接修改state
  if (action.index < state.deviceState.deviceFilterMap.length) {
    state.deviceState.selectedFloor = filterModel.floorName;     // ❌ 直接修改
    state.deviceState.selectedRoom = filterModel.roomName;       // ❌ 直接修改
    state.deviceState.selectedRoomId = filterModel.roomId;       // ❌ 直接修改
  }
  return state;  // ❌ 返回被修改的原state
}
```

#### ✅ **正确用法 - 使用Freezed的copyWith**
>
>引入freezed库后，运行flutter packages pub run build_runner build

```dart
// lib/smarthome/store/smarthome_reducer.dart
SmartHomeState _initSmartHome(SmartHomeState state, SmartHomeInitializedAction action) {
  // 从mainParamsMap中获取参数
  final String? isLoginStr = action.mainParamsMap['isLogin'];
  final String? familyId = action.mainParamsMap['familyId'];
  final String? familyName = action.mainParamsMap['familyName'];
  
  // 转换isLogin字符串为bool
  bool isLogin = false;
  if (isLoginStr != null) {
    isLogin = isLoginStr.toLowerCase() == 'true';
  }
  
  // 更新authState
  final AuthState newAuthState = state.authState.copyWith(isLogin: isLogin);
  
  // 更新familyState
  final FamilyState newFamilyState = state.familyState.copyWith(
    familyId: familyId ?? '',
    familyName: familyName ?? '',
  );
  
  return state.copyWith(
    authState: newAuthState,
    familyState: newFamilyState,
  );
}
```

### 2. **每个应用程序只有一个 Redux Store**
>
>一个标准的 Redux 应用应有且仅有一个 Store 实例（单例），供整个应用使用。它应该用一个单独的文件比如store.js定义出来。

理想情况下，不应该有任意一个应用逻辑将其直接引入。他应该使用通过`<StoreProvider>` 传递给 React 组件树，或通过 middlewares 间接引用。在极少数的用例中，你可能需要将其导入其他逻辑文件，但这应该是没有办法的办法。

#### ✅ **正确用法 - SmartHomeDispatcher单例模式**

```dart
// lib/smarthome/store/smarthome_dispatcher.dart
class SmartHomeDispatcher {
  static SmartHomeDispatcher? _instance;
  late final Store<SmartHomeState> _store;

  SmartHomeDispatcher._() {
    _store = Store<SmartHomeState>(
      smartHomeReducer,
      initialState: SmartHomeState.initial(),
      middleware: smartHomeMiddleware,
    );
  }

  /// 获取单例实例
  static SmartHomeDispatcher get instance {
    _instance ??= SmartHomeDispatcher._();
    return _instance!;
  }

  /// 获取当前状态
  SmartHomeState get currentState => _store.state;

  /// 获取Store（用于StoreProvider）
  Store<SmartHomeState> get store => _store;

  /// 通用dispatch方法
  void dispatch(BaseAction action) {
    _store.dispatch(action);
  }
}

  late final Store<SmartHomeState> _store;

  SmartHomeDispatcher._() {
    _store = Store<SmartHomeState>(
      smartHomeReducer,
      initialState: SmartHomeState.initial(),
      middleware: smartHomeMiddleware,
    );
  }

  /// 获取单例实例
  static SmartHomeDispatcher get instance {
    _instance ??= SmartHomeDispatcher._();
    return _instance!;
  }

  /// 获取当前状态
  SmartHomeState get currentState => _store.state;

  /// 获取Store（用于StoreProvider）
  Store<SmartHomeState> get store => _store;

  /// 通用dispatch方法
  void dispatch(BaseAction action) {
    _store.dispatch(action);
  }
}

// 在UI中使用StoreProvider
// lib/smarthome/smarthome.dart
return StoreProvider<SmartHomeState>(
  store: SmartHomeDispatcher.instance.store,
  child: const Scaffold(
    body: Column(
      children: <Widget>[
        NavigatorWidget(),
        Expanded(
          child: Center(
            child: DeviceWidget(),
          ),
        ),
      ],
    ),
  ),
);

// 通过StoreConnector访问
class NavigatorWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, String>(
      distinct: true,
      converter: NavigatorSelectors.displayNameSelector,
      builder: (BuildContext context, String displayName) {
        return Container(
          // ... UI构建逻辑
        );
      },
    );
  }
}

// 在Middleware中访问
class SmartHomeMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    if (action is SmartHomeInitializedAction) {
      // 1. 处理副作用逻辑
      final bool isLogin = await MockUser.getLoginStatus();
      // 2. 处理完毕后，发送新的Action
      store.dispatch(UserLoginStatusChangedAction(isLogin: isLogin));
    }
    // 3. 继续传递原始action
    next(action);
  }
}

```

#### ❌ **错误用法 - 直接导入Store**

```dart
import 'package:smart_home/store/smart_home_store.dart';

class SomeService {
  void someMethod() {
    // 直接使用store - 不推荐
    smartHomeStore.dispatch(SomeAction());
  }
}
```

### 3. **不要在 State 或 Action 中放入不可序列化的值**
>
>不要把不可序列化的值放进 Redux store 或者 dispatch 的 actions。这一点保证 Redux DevTools 按照预期方式工作。也保证 UI 按照预期方式更新。
>
> ❌ 禁止在 State 或 Action 中放入以下不可序列化的值：
Future/Promise - 异步操作对象
Stream - 数据流对象
Timer - 定时器对象
BuildContext - Flutter上下文对象
Widget - Flutter组件对象
Function/Closure - 函数/闭包
类的实例 - 自定义类的对象实例
Map/Set - 集合对象（除非只包含可序列化值）
Symbol - 符号对象

#### ❌ **错误用法 - 存储BuildContext**

```dart
// lib/device/store/device_action.dart:181
class UpdatePopupContextAction extends DeviceBaseAction {
  UpdatePopupContextAction(this.context);
  final BuildContext? context;  // ❌ 存储UI对象BuildContext
}
```

#### ✅ **正确用法 - 序列化数据**

```dart
// Action只包含数据
class SmartHomeInitializedAction extends BaseAction {
  final Map<String, String> mainParamsMap;
  SmartHomeInitializedAction({required this.mainParamsMap});
}

class UserLoginStatusChangedAction extends BaseAction {
  final bool isLogin;
  UserLoginStatusChangedAction({required this.isLogin});
}

// UI层处理导航
StoreListener<SmartHomeState>(
  onChanged: (context, state) {
    if (state.authState.isLogin) {
      // 在UI层处理导航逻辑
    }
  },
  child: NavigatorWidget(),
)
```

### 4. **使用 Freezed确保State不可变更新**

#### **Freezed的优势**

- **自动生成不可变代码** - 所有字段都是final的
- **自动生成copyWith方法** - 返回新实例，确保不可变性
- **自动生成equals、hashCode、toString** - 减少样板代码
- **编译时检查** - 确保状态更新符合Redux原则
- **无需添加@immutable** - Freezed已经提供了不可变性保证

#### ❌ **错误用法 - 手写样板代码**

```dart
// lib/store/smart_home_state.dart:72
SmartHomeState copyWith({
  bool? isEditState,
  bool? isScrollableScrollPhysics,
  bool? isLogin,
  FamilyState? familyState,
  WholeHouseState? wholeHouseState,
  // ... 13个参数，大量重复代码
}) {
  return SmartHomeState(
      isEditState: isEditState ?? this.isEditState,
      isScrollableScrollPhysics: isScrollableScrollPhysics ?? this.isScrollableScrollPhysics,
      isLogin: isLogin ?? this.isLogin,
      familyState: familyState ?? this.familyState,
      // ... 还有很多重复代码
  );
}
```

#### ✅ **正确用法 - Freezed自动生成**

```dart
// lib/smarthome/store/smarthome_state.dart
@freezed
class SmartHomeState with _$SmartHomeState {
  const factory SmartHomeState({
    @Default(AuthState()) AuthState authState,
    @Default(FamilyState()) FamilyState familyState,
  }) = _SmartHomeState;

  // 提供初始状态的方法
  static SmartHomeState initial() {
    return const SmartHomeState();
  }
}

// lib/features/auth/store/auth_state.dart
@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isLogin,
  }) = _AuthState;
}

// 从80行代码减少到15行代码！
```

#### **Freezed使用注意事项**

- ✅ **只需要@freezed注解** - 无需额外添加@immutable
- ✅ **自动生成不可变代码** - 所有字段都是final的
- ✅ **自动生成copyWith** - 确保状态更新符合Redux原则
- ✅ **编译时检查** - 防止意外修改状态
- ✅ **类型安全** - 编译时类型检查

---

## **Action 设计规范**

> **Action职责**：描述应用中"发生了什么事件"
应该放：事件名称、相关的业务数据、事件上下文信息；
不应该放：业务逻辑、UI操作、异步处理、函数回调；

### 1. **Action命名：事件风格 + 语义化**

#### ❌ **错误用法 - Setter风格和命名不一致**

```dart
// lib/device/store/device_action.dart
class UpdateDeviceTabIndexAction extends DeviceBaseAction {
  UpdateDeviceTabIndexAction(this.index);
  final int index;  // ❌ 简单的setter风格
}

class UpdateDeviceStatusAction extends DeviceBaseAction {
  UpdateDeviceStatusAction(this.deviceStatus);
  final DeviceStatus deviceStatus;  // ❌ 直接设置状态
}

class SmallCardDragFinishedAction extends DeviceBaseAction {}  // ❌ 不一致的模式
class UpdateImageRefreshCountAction {}                        // ❌ 没有继承BaseAction
class FetchCameraMsgAction extends DeviceBaseAction {}        // ❌ 缺少Data后缀
```

#### ✅ **正确用法 - 事件风格 + 统一命名**

```dart
// 应用级别Action - 描述"发生了什么"而不是"设置什么"
class SmartHomeInitializedAction extends BaseAction {
  final Map<String, String> mainParamsMap;
  SmartHomeInitializedAction({required this.mainParamsMap});
}

class SmartHomeAppearedAction extends BaseAction {
  SmartHomeAppearedAction();
}

class SmartHomeDisappearedAction extends BaseAction {
  SmartHomeDisappearedAction();
}

// 模块级别Action - 使用事件风格命名
class UserLoginStatusChangedAction extends BaseAction {
  final bool isLogin;
  UserLoginStatusChangedAction({required this.isLogin});
}

class FamilyInfoChangedAction extends BaseAction {
  final String familyId;
  final String familyName;
  FamilyInfoChangedAction({
    required this.familyId,
    required this.familyName,
  });
}

class DeviceListChangedAction extends BaseAction {
  final List<dynamic> deviceList;
  DeviceListChangedAction({required this.deviceList});
}

class WholeHouseInfoChangedAction extends BaseAction {
  final String wholeHouseInfo;
  WholeHouseInfoChangedAction({required this.wholeHouseInfo});
}
```

### 2. **BaseAction基类规范**

所有Action都应继承自 `BaseAction` 基类，确保类型安全和统一性：

```dart
// lib/smarthome/store/smarthome_actions.dart
class BaseAction {}

// 所有模块的Action都应继承BaseAction
class UserLoginStatusChangedAction extends BaseAction {
  final bool isLogin;
  UserLoginStatusChangedAction({required this.isLogin});
}
```

### 3. **统一使用命名参数**

#### ❌ **错误用法 - 参数风格混合**

```dart
// lib/device/store/device_action.dart:23
class UpdateDeviceInfoMapAction extends DeviceBaseAction {              // ❌ Setter方式命名
  UpdateDeviceInfoMapAction(this.originalDeviceInfoMap, this.familyId,  // ❌ 位置参数
      {required this.traceId, required this.traceType});                // ❌ 混合命名参数
  
  int traceId;
  TraceType traceType;
  String familyId;
  Map<String, DeviceInfoModel> originalDeviceInfoMap;
}

// lib/scene/store/scene_action.dart:28
class SceneExecuteAction extends SceneBaseAction {
  SceneExecuteAction(this.item, this.context);  // ❌ 纯位置参数，容易搞错顺序
  SceneItemViewModel item;
  BuildContext context;
}
```

#### ✅ **正确用法 - 命名参数**

```dart
class DeviceInfoMapChangedAction extends DeviceBaseAction {
  final Map<String, DeviceInfoModel> originalDeviceInfoMap;
  final String familyId;
  final int traceId;
  final TraceType traceType;

  DeviceInfoMapChangedAction({
    required this.originalDeviceInfoMap,
    required this.familyId,
    required this.traceId,
    required this.traceType,
  });
}

class SceneExecuteAction extends SceneBaseAction {
  final String sceneId;
  final String sceneName;
  final String familyId;
  
  SceneExecuteAction({
    required this.sceneId,
    required this.sceneName,
    required this.familyId,
  });
}
```

---

## **Selectors 使用规范**

> **Selector职责**：从Store中提取和转换数据，为UI提供所需的数据格式
应该放：数据选择逻辑、数据转换逻辑、计算属性；
不应该放：业务逻辑、UI操作、异步处理、副作用操作；

### 1. **Selector命名规范**

```dart
// lib/features/navigator/selectors/navigator_selectors.dart
class NavigatorSelectors {
  /// 获取导航条显示名称的selector
  static String displayNameSelector(Store<SmartHomeState> store) {
    final bool isLogin = store.state.authState.isLogin;
    final String familyName = store.state.familyState.familyName;

    if (isLogin && familyName.isNotEmpty) {
      return familyName;
    } else {
      return '请登录';
    }
  }
}
```

### 2. **Selector使用场景**

- **数据选择**：从复杂状态中提取特定数据
- **数据转换**：将原始数据转换为UI所需的格式
- **计算属性**：基于多个状态值计算派生数据
- **性能优化**：避免在UI中重复计算相同的数据

---

## **Reducer 编写规范**

> **Reducer职责**：根据Action更新State，必须是纯函数
应该放：状态计算逻辑、数据转换逻辑；
不应该放：API调用、UI操作、异步处理、副作用操作、日志输出；

### 1. **Reducer 命名使用_updateReducerXXXDataByY 或 _updateReducerXXXData(,)的风格**

### 2. **Reducer 不能有副作用**
>
>Reducer 函数必须只 依赖于 state 和 action 参数，且必须重新计算并返回一个新的 state。其中禁止执行任何异步代码（Future<>），生成随机值 （Date.now(), Math.random()，修改在 reducer 外面定义的变量，或者执行一些修改 reducer 函数作用域之外变量的代码。
>
>注意：只要符合同样的规则，在 reducer 中调用外部定义的一些方法，比如从库或工具类中 import 的函数等，也是可以的。
>
>**详细说明:**
此规则的目的是确保在调用时 reducers 的行为可预测。例如，如果你正在进行时间旅行调试，则可能会多次调用 reducer 函数，并根据之前的 actions 生成"当前"状态值。如果 reducer 有副作用，这将导致在调试过程中产生了这些副作用，并导致应用程序以意料之外的方式运行。
>
>这条规则也有一些灰色地带。严格来说，有一些代码也是副作用，比如 Log.debug(state)，但它对应用程序没有实质行的行为。

#### ❌ **错误用法 - Reducer包含副作用**

```dart
// lib/device/store/device_reducer.dart:225 - 在Reducer中调用UI操作
SmartHomeState _updateDeviceFilter(SmartHomeState state, UpdateDeviceFilterAction action) {
  // ... 状态更新逻辑
  if (tabIndex >= 0) {
    SmartHomePresenter.changeTab(tabIndex);  // ❌ 副作用：UI操作
    state.deviceState.deviceTabIndex = tabIndex;
    // ...
  }
  return state;
}
```

#### ✅ **正确用法 - 纯函数Reducer**

```dart
// lib/smarthome/store/smarthome_reducer.dart - 应用级别Reducer
final Reducer<SmartHomeState> smartHomeReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  // 应用级别的reducer
  TypedReducer<SmartHomeState, SmartHomeInitializedAction>(_initSmartHome).call,

  // 模块级别的reducer
  authCombineReducer,
]);

// 应用级别的reducer函数
SmartHomeState _initSmartHome(SmartHomeState state, SmartHomeInitializedAction action) {
  // 从mainParamsMap中获取参数
  final String? isLoginStr = action.mainParamsMap['isLogin'];
  final String? familyId = action.mainParamsMap['familyId'];
  final String? familyName = action.mainParamsMap['familyName'];
  
  // 转换isLogin字符串为bool
  bool isLogin = false;
  if (isLoginStr != null) {
    isLogin = isLoginStr.toLowerCase() == 'true';
  }
  
  // 更新authState
  final AuthState newAuthState = state.authState.copyWith(isLogin: isLogin);
  
  // 更新familyState
  final FamilyState newFamilyState = state.familyState.copyWith(
    familyId: familyId ?? '',
    familyName: familyName ?? '',
  );
  
  return state.copyWith(
    authState: newAuthState,
    familyState: newFamilyState,
  );
}

// lib/features/auth/store/auth_reducer.dart - 模块级别Reducer
final Reducer<SmartHomeState> authCombineReducer = combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UserLoginStatusChangedAction>(_updateLoginStatus).call,
]);

// 更新登录状态
SmartHomeState _updateLoginStatus(SmartHomeState state, UserLoginStatusChangedAction action) {
  return state.copyWith(
    authState: state.authState.copyWith(isLogin: action.isLogin),
  );
}

  TypedReducer<AuthState, UserLoginStatusChangedAction>(_updateLoginStatus),
]);

AuthState _updateLoginStatus(AuthState state, UserLoginStatusChangedAction action) {
  return state.copyWith(isLogin: action.isLogin);
}

```

---

## **Middleware 使用规范**

> **Middleware职责**：处理所有副作用和复杂逻辑
应该放：API调用、异步操作、UI操作、日志记录、缓存处理、业务流程控制；
不应该放：状态计算逻辑（应在Reducer中）；

### 1. **所有副作用必须在Middleware中处理**

#### ✅ **正确用法 - Middleware处理副作用**

```dart
// lib/smarthome/store/smarthome_middleware.dart
final List<Middleware<SmartHomeState>> smartHomeMiddleware =
    <Middleware<SmartHomeState>>[SmartHomeMiddleware().call];

class SmartHomeMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    switch (action.runtimeType) {
      case SmartHomeInitializedAction:
        final SmartHomeInitializedAction initAction = action as SmartHomeInitializedAction;

        // 检查并获取缺失的参数
        final String? isLoginStr = initAction.mainParamsMap['isLogin'];
        final String? familyId = initAction.mainParamsMap['familyId'];
        final String? familyName = initAction.mainParamsMap['familyName'];

        // 如果isLogin参数缺失，则获取登录状态
        if (isLoginStr == null || isLoginStr.isEmpty) {
          final bool isLogin = await MockUser.getLoginStatus();
          SmartHomeDispatcher.instance
              .dispatch(UserLoginStatusChangedAction(isLogin: isLogin));
        }

        // 如果familyId或familyName参数缺失，则获取家庭信息
        if (familyId == null ||
            familyId.isEmpty ||
            familyName == null ||
            familyName.isEmpty) {
          final Map<String, String> familyInfo =
              await MockFamily.getFamilyInfo();
          SmartHomeDispatcher.instance.dispatch(FamilyInfoChangedAction(
              familyId: familyInfo['familyId'] ?? '',
              familyName: familyInfo['familyName'] ?? ''));
        }
        break;
    }
    next(action);
  }
}
```

### 2. **SmartHome生命周期函数与业务模块Middleware协作**

#### **生命周期函数职责**
>
> SmartHome作为根视图，其生命周期函数负责发送对应的Action，各业务模块的Middleware自行拦截处理各自的副作用。

#### ✅ **正确用法 - 生命周期函数发送Action**

```dart
// lib/smarthome/smarthome.dart
class _SmartHomePageState extends State<SmartHomePage> {
  @override
  void initState() {
    super.initState();
    // 初始化传入的启动参数
    SmartHomeDispatcher.instance.dispatch(
        SmartHomeInitializedAction(mainParamsMap: widget.mainParamsMap));
  }

  void didAppear([Map<dynamic, dynamic>? args]) {
    // 页面出现时发送Action
    SmartHomeDispatcher.instance.dispatch(SmartHomeAppearedAction());
  }

  void didDisappear() {
    // 页面消失时发送Action
    SmartHomeDispatcher.instance.dispatch(SmartHomeDisappearedAction());
  }

  @override
  Widget build(BuildContext context) {
    return StoreProvider<SmartHomeState>(
      store: SmartHomeDispatcher.instance.store,
      child: const Scaffold(
        body: Column(
          children: <Widget>[
            NavigatorWidget(),
            Expanded(
              child: Center(
                child: DeviceWidget(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

#### ✅ **正确用法 - 业务模块Middleware拦截处理**

```dart
// lib/features/device/store/device_middleware.dart
class DeviceMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    switch (action.runtimeType) {
      case SmartHomeInitializedAction:
        _initializeDevicePlugins(store, action);
        break;
      case SmartHomeAppearedAction:
        _handleSmartHomeAppeared(store, action);
        break;
      case SmartHomeDisappearedAction:
        _handleSmartHomeDisappeared(store, action);
        break;
      default:
        next(action);
        break;
    }
    next(action);
  }
}

Future<void> _initializeDevicePlugins(Store<SmartHomeState> store, dynamic action) async {
  // 设备模块的初始化逻辑
  UpDevicePlugin.subscribeDeviceListChange(_deviceListChangeCallback)
      .catchError((dynamic error) {
    DevLogger.error(
        tag: SmartHomeConstant.package,
        msg: 'UpDevicePlugin.subscribeDeviceListChange error $error');
  });
}

void _handleSmartHomeAppeared(Store<SmartHomeState> store, dynamic action) {
  // 页面出现时的设备模块逻辑
  // 例如：刷新设备列表、恢复设备状态等
  DevLogger.debug(
      tag: SmartHomeConstant.package,
      msg: 'Device module: SmartHome appeared');
}

void _handleSmartHomeDisappeared(Store<SmartHomeState> store, dynamic action) {
  // 页面消失时的设备模块逻辑
  // 例如：暂停设备监听、保存设备状态等
  DevLogger.debug(
      tag: SmartHomeConstant.package,
      msg: 'Device module: SmartHome disappeared');
}

void _deviceListChangeCallback() {
  // 处理设备列表变化
  SmartHomeDispatcher.instance.dispatch(DeviceListChangedAction());
}
```

#### ✅ **正确用法 - 其他业务模块Middleware**

```dart
// lib/features/auth/store/auth_middleware.dart
class AuthMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    switch (action.runtimeType) {
      case SmartHomeInitializedAction:
        _initializeAuthModule(store, action);
        break;
      case SmartHomeAppearedAction:
        _handleSmartHomeAppeared(store, action);
        break;
      default:
        next(action);
        break;
    }
    next(action);
  }
}

Future<void> _initializeAuthModule(Store<SmartHomeState> store, dynamic action) async {
  // 认证模块的初始化逻辑
  final bool isLogin = await MockUser.getLoginStatus();
  store.dispatch(UserLoginStatusChangedAction(isLogin: isLogin));
}

void _handleSmartHomeAppeared(Store<SmartHomeState> store, dynamic action) {
  // 页面出现时的认证模块逻辑
  DevLogger.debug(
      tag: SmartHomeConstant.package,
      msg: 'Auth module: SmartHome appeared');
}
```

### 3. **不影响State数据的副作用必须在相应Widget对应的Utils中实现**

```dart
// lib/features/device/widget/device_widget.dart
class DeviceWidget extends StatelessWidget {
  const DeviceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: SizedBox(
        height: 50,
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            // 这里可以添加按钮点击事件
            DeviceUiTools.deviceControlBtnPressed(context);
          },
          child: const Text('设备控制'),
        ),
      ),
    );
  }
}

// lib/features/device/widget/device_ui_tools.dart
import 'package:flutter/material.dart';

class DeviceUiTools {
  static Future<void> deviceControlBtnPressed(BuildContext context) async {
    //模拟UI上点击属性控制一类的，无需更新数据
    return Future<void>.delayed(const Duration(seconds: 2), () {
      showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return const AlertDialog(
            title: Text('设备控制成功！'),
          );
        },
      );
    });
  }
}
```

---

## **StoreConnector 使用规范**

> **StoreConnector职责**：连接Redux Store和UI组件
应该放：状态选择逻辑、UI数据转换；
不应该放：复杂的业务逻辑、直接的状态修改、副作用操作；
**如果widget间有展示顺序的依赖关系**则通过StoreConnector处理，如先展示导航条，再展示设备列表。则设备列表的展示条件为：导航条展示 && 设备列表数据组装成功

### 1. **使用 distinct: true 避免 UI 重绘**

#### ✅ **正确用法 - 使用distinct**

```dart
// lib/features/navigator/widget/navigator.dart
StoreConnector<SmartHomeState, String>(
  distinct: true,  // ✅ 正确使用distinct避免不必要重建
  converter: NavigatorSelectors.displayNameSelector,
  builder: (BuildContext context, String displayName) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        displayName,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  },
)
```

### 2. **避免在组件中直接访问全局Store**

#### ❌ **错误用法 - 直接访问Store**

```dart
// 避免这样直接访问全局store
class BadDeviceWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        // ❌ 直接访问全局store
        smartHomeStore.dispatch(UpdateDeviceTabIndexAction(1));
      },
      child: Text('切换设备'),
    );
  }
}
```

#### ✅ **正确用法 - StoreProvider**

```dart
// ✅ 正确的简化版本
StoreConnector<SmartHomeState, SomeViewModel>(
  converter: (store) => SomeViewModel(
    data: store.state.someData,
    isLoading: store.state.isLoading,
  ),
  builder: (context, viewModel) {    
    return SomeWidget(
      viewModel: viewModel,
      onAction: () => SmartHomeDispatcher.instance
              .dispatch(SomeAction()),  // ✅ 注意没有分号
    );
  },
)
```

## **日志使用规范**

> **日志职责**：记录应用运行状态和调试信息
应该放：使用DevLogger进行日志记录；
不应该放：使用print、debugPrint等直接输出；

### 1. **统一使用DevLogger**

#### ✅ **正确用法 - DevLogger**

```dart
// lib/common/services/http_service.dart
import 'package:device_utils/log/log.dart';
import '../constants/smarthome_constant.dart';

class HttpService {
  static Future<EnvDeviceInfosModel?> getEnvDeviceInfos(String familyId) async {
    try {
      final EnvDeviceInfosModel result = await _api.getEnvDeviceInfos(familyId);
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'getEnvDevices: $result',
      );
      return result;
    } catch (e) {
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'getEnvDevices err: $e',
      );
      return null;
    }
  }
}
```

#### ❌ **错误用法 - print**

```dart
// 避免使用print
print('getEnvDevices: $result');  // ❌ 不推荐
debugPrint('getEnvDevices err: $e');  // ❌ 不推荐
```


---

## **重构优先级**

### **P0 - 立即修改**

1. **确保所有State使用Freezed**：检查是否还有手写的copyWith方法
2. **统一Action命名风格**：全部使用Setter风格和命名参数
3. **添加distinct: true**：所有StoreConnector都添加distinct参数

### **P1 - 近期修改**

1. **完善Selectors**：为所有UI组件创建对应的Selectors
2. **优化Middleware结构**：拆分复杂Middleware为多个单一职责的Middleware

### **P2 - 长期规划**

1. **添加单元测试**：为Reducer、Middleware、Selectors添加测试
2. **性能优化**：使用memoization优化Selectors性能
3. **文档完善**：补充更多实际使用场景的示例

---
