import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../../features/wholehouse/services/env_devices_response_model.dart';

part 'rest_client.g.dart';

@RestApi()
abstract class SmartHomeRestClient {
  factory SmartHomeRestClient(Dio dio, {String baseUrl}) = _SmartHomeRestClient;

  /// 获取环境设备数据
  @POST('/api-gw/wisdomdevice/device/v1/queryEnvPreference')
  Future<EnvDevicesData> getEnvDevices(@Body() Map<String, String> params,
      @Header('appVersion') String appVersion);
}
