// ignore: depend_on_referenced_packages

import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter_redux_demo/common/services/rest_client.dart';
import 'package:upservice/dio/uhome_dio/uhome_dio.dart';

import '../../features/wholehouse/services/env_devices_response_model.dart';
import '../constants/smarthome_constant.dart';

class HttpService {
  static String _appVersion = '';

  static Future<String> getAppVersion() async {
    if (_appVersion.isEmpty) {
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      _appVersion = appInfo.appVersion;
    }
    return _appVersion;
  }

  static Future<EnvDevicesData?> getEnvDeviceInfos(String familyId) async {
    try {
      final Map<String, String> params = <String, String>{
        'familyId': familyId,
      };
      final String appVersion = await getAppVersion();

      final EnvDevicesData responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .getEnvDevices(params, appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'getEnvDevices : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'getEnvDevices err: $err');
      return null;
    }
  }
}
