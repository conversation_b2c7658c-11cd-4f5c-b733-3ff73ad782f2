import 'package:flutter/material.dart';
import 'smarthome/smarthome.dart';

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '智能家居 Redux Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const SmartHomePage(mainParamsMap: <String, String>{
        'isLogin': 'true',
        'familyId': 'family_001',
        'familyName': '张的家',
      }),
      debugShowCheckedModeBanner: false,
    );
  }
}
