import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';

import '../features/device/widgets/device_widget.dart';
import '../features/navigator/widgets/navigator.dart';
import '../smarthome/store/smarthome_actions.dart';
import '../smarthome/store/smarthome_dispatcher.dart';
import '../smarthome/store/smarthome_state.dart';

class SmartHomePage extends StatefulWidget {
  final Map<String, String> mainParamsMap;

  const SmartHomePage({super.key, required this.mainParamsMap});

  @override
  State<StatefulWidget> createState() => _SmartHomePageState();
}

class _SmartHomePageState extends State<SmartHomePage> {
  @override
  void initState() {
    super.initState();

    // 初始化传入的启动参数
    SmartHomeDispatcher.instance.dispatch(
        SmartHomeInitializedAction(mainParamsMap: widget.mainParamsMap));
  }

  void didAppear([Map<dynamic, dynamic>? args]) {
    SmartHomeDispatcher.instance.dispatch(SmartHomeAppearedAction());
  }

  void didDisappear() {
    SmartHomeDispatcher.instance.dispatch(SmartHomeDisappearedAction());
  }

  @override
  Widget build(BuildContext context) {
    // 在SmartHomePage中挂接store
    return StoreProvider<SmartHomeState>(
      store: SmartHomeDispatcher.instance.store,
      child: const Scaffold(
        body: Column(
          children: <Widget>[
            // 顶部导航条
            NavigatorWidget(),
            // 中间内容区域
            Expanded(
              child: Center(
                child: DeviceWidget(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
