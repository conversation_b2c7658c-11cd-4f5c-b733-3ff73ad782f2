import 'package:flutter_redux_demo/smarthome/store/smarthome_middleware.dart';
import 'package:redux/redux.dart';
import 'smarthome_actions.dart';
import 'smarthome_reducer.dart';
import 'smarthome_state.dart';

/// 智能家居Dispatcher - 隐藏Store实现细节
class SmartHomeDispatcher {
  static SmartHomeDispatcher? _instance;
  late final Store<SmartHomeState> _store;

  SmartHomeDispatcher._() {
    _store = Store<SmartHomeState>(
      smartHomeReducer,
      initialState: SmartHomeState.initial(),
      middleware: smartHomeMiddleware,
    );
  }

  /// 获取单例实例
  static SmartHomeDispatcher get instance {
    _instance ??= SmartHomeDispatcher._();
    return _instance!;
  }

  /// 获取当前状态
  SmartHomeState get currentState => _store.state;

  /// 获取Store（用于StoreProvider）
  Store<SmartHomeState> get store => _store;

  /// 通用dispatch方法
  void dispatch(BaseAction action) {
    _store.dispatch(action);
  }
}
