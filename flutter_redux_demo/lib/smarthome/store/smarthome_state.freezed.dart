// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'smarthome_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SmartHomeState {
  AuthState get authState => throw _privateConstructorUsedError;
  FamilyState get familyState => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SmartHomeStateCopyWith<SmartHomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SmartHomeStateCopyWith<$Res> {
  factory $SmartHomeStateCopyWith(
          SmartHomeState value, $Res Function(SmartHomeState) then) =
      _$SmartHomeStateCopyWithImpl<$Res, SmartHomeState>;
  @useResult
  $Res call({AuthState authState, FamilyState familyState});

  $AuthStateCopyWith<$Res> get authState;
  $FamilyStateCopyWith<$Res> get familyState;
}

/// @nodoc
class _$SmartHomeStateCopyWithImpl<$Res, $Val extends SmartHomeState>
    implements $SmartHomeStateCopyWith<$Res> {
  _$SmartHomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authState = null,
    Object? familyState = null,
  }) {
    return _then(_value.copyWith(
      authState: null == authState
          ? _value.authState
          : authState // ignore: cast_nullable_to_non_nullable
              as AuthState,
      familyState: null == familyState
          ? _value.familyState
          : familyState // ignore: cast_nullable_to_non_nullable
              as FamilyState,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AuthStateCopyWith<$Res> get authState {
    return $AuthStateCopyWith<$Res>(_value.authState, (value) {
      return _then(_value.copyWith(authState: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $FamilyStateCopyWith<$Res> get familyState {
    return $FamilyStateCopyWith<$Res>(_value.familyState, (value) {
      return _then(_value.copyWith(familyState: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SmartHomeStateImplCopyWith<$Res>
    implements $SmartHomeStateCopyWith<$Res> {
  factory _$$SmartHomeStateImplCopyWith(_$SmartHomeStateImpl value,
          $Res Function(_$SmartHomeStateImpl) then) =
      __$$SmartHomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AuthState authState, FamilyState familyState});

  @override
  $AuthStateCopyWith<$Res> get authState;
  @override
  $FamilyStateCopyWith<$Res> get familyState;
}

/// @nodoc
class __$$SmartHomeStateImplCopyWithImpl<$Res>
    extends _$SmartHomeStateCopyWithImpl<$Res, _$SmartHomeStateImpl>
    implements _$$SmartHomeStateImplCopyWith<$Res> {
  __$$SmartHomeStateImplCopyWithImpl(
      _$SmartHomeStateImpl _value, $Res Function(_$SmartHomeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authState = null,
    Object? familyState = null,
  }) {
    return _then(_$SmartHomeStateImpl(
      authState: null == authState
          ? _value.authState
          : authState // ignore: cast_nullable_to_non_nullable
              as AuthState,
      familyState: null == familyState
          ? _value.familyState
          : familyState // ignore: cast_nullable_to_non_nullable
              as FamilyState,
    ));
  }
}

/// @nodoc

class _$SmartHomeStateImpl implements _SmartHomeState {
  const _$SmartHomeStateImpl(
      {this.authState = const AuthState(),
      this.familyState = const FamilyState()});

  @override
  @JsonKey()
  final AuthState authState;
  @override
  @JsonKey()
  final FamilyState familyState;

  @override
  String toString() {
    return 'SmartHomeState(authState: $authState, familyState: $familyState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SmartHomeStateImpl &&
            (identical(other.authState, authState) ||
                other.authState == authState) &&
            (identical(other.familyState, familyState) ||
                other.familyState == familyState));
  }

  @override
  int get hashCode => Object.hash(runtimeType, authState, familyState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SmartHomeStateImplCopyWith<_$SmartHomeStateImpl> get copyWith =>
      __$$SmartHomeStateImplCopyWithImpl<_$SmartHomeStateImpl>(
          this, _$identity);
}

abstract class _SmartHomeState implements SmartHomeState {
  const factory _SmartHomeState(
      {final AuthState authState,
      final FamilyState familyState}) = _$SmartHomeStateImpl;

  @override
  AuthState get authState;
  @override
  FamilyState get familyState;
  @override
  @JsonKey(ignore: true)
  _$$SmartHomeStateImplCopyWith<_$SmartHomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
