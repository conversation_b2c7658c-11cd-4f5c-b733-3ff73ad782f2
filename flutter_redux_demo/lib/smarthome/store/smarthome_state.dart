import 'package:freezed_annotation/freezed_annotation.dart';
import '../../features/auth/store/auth_state.dart';
import '../../features/family/store/family_state.dart';

part 'smarthome_state.freezed.dart';

@freezed
class SmartHomeState with _$SmartHomeState {
  const factory SmartHomeState({
    @Default(AuthState()) AuthState authState,
    @Default(FamilyState()) FamilyState familyState,
  }) = _SmartHomeState;

  // 提供初始状态的方法
  static SmartHomeState initial() {
    return const SmartHomeState();
  }
}
