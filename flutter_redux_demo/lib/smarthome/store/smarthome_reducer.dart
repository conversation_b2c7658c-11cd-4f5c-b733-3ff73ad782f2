import 'package:flutter_redux_demo/features/auth/store/auth_reducer.dart';
import 'package:flutter_redux_demo/features/auth/store/auth_state.dart';
import 'package:flutter_redux_demo/features/family/store/family_state.dart';
import 'package:flutter_redux_demo/smarthome/store/smarthome_actions.dart';
import 'package:flutter_redux_demo/smarthome/store/smarthome_state.dart';
import 'package:redux/redux.dart';

// 智能家居Reducer - 使用combineReducers组合各个模块的reducer
final Reducer<SmartHomeState> smartHomeReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  // 应用级别的reducer
  TypedReducer<SmartHomeState, SmartHomeInitializedAction>(_initSmartHome).call,

  // 模块级别的reducer
  authCombineReducer,
]);

// 应用级别的reducer函数
SmartHomeState _initSmartHome(
    SmartHomeState state, SmartHomeInitializedAction action) {
  // 从mainParamsMap中获取参数
  final String? isLoginStr = action.mainParamsMap['isLogin'];
  final String? familyId = action.mainParamsMap['familyId'];
  final String? familyName = action.mainParamsMap['familyName'];

  // 转换isLogin字符串为bool
  bool isLogin = false;
  if (isLoginStr != null) {
    isLogin = isLoginStr.toLowerCase() == 'true';
  }

  // 更新authState
  final AuthState newAuthState = state.authState.copyWith(isLogin: isLogin);

  // 更新familyState
  final FamilyState newFamilyState = state.familyState.copyWith(
    familyId: familyId ?? '',
    familyName: familyName ?? '',
  );

  return state.copyWith(
    authState: newAuthState,
    familyState: newFamilyState,
  );
}
