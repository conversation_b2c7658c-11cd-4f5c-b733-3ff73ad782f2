import 'package:flutter_redux_demo/smarthome/store/smarthome_actions.dart';
import 'package:flutter_redux_demo/smarthome/store/smarthome_dispatcher.dart';
import 'package:flutter_redux_demo/smarthome/store/smarthome_state.dart';
import 'package:redux/redux.dart';

import '../../Mock/mock_family.dart';
import '../../Mock/mock_user.dart';
import '../../features/auth/store/auth_actions.dart';
import '../../features/family/store/family_actions.dart';

final List<Middleware<SmartHomeState>> smartHomeMiddleware =
    <Middleware<SmartHomeState>>[SmartHomeMiddleware().call];

class SmartHomeMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    switch (action.runtimeType) {
      case SmartHomeInitializedAction:
        _initSmartHome(store, action);
        break;
      case SmartHomeAppearedAction:
        _initSmartHome(store, action);
        break;
      case SmartHomeDisappearedAction:
        _initSmartHome(store, action);
        break;
      default:
        next(action);
        break;
    }
    next(action);
  }
}

Future<void> _initSmartHome(Store<SmartHomeState> store, dynamic action) async {
  final SmartHomeInitializedAction initAction =
      action as SmartHomeInitializedAction;

  // 检查并获取缺失的参数
  final String? isLoginStr = initAction.mainParamsMap['isLogin'];
  final String? familyId = initAction.mainParamsMap['familyId'];
  final String? familyName = initAction.mainParamsMap['familyName'];

  // 如果isLogin参数缺失，则获取登录状态
  if (isLoginStr == null || isLoginStr.isEmpty) {
    final bool isLogin = await MockUser.getLoginStatus();
    SmartHomeDispatcher.instance
        .dispatch(UserLoginStatusChangedAction(isLogin: isLogin));
  }

  // 如果familyId或familyName参数缺失，则获取家庭信息
  if (familyId == null ||
      familyId.isEmpty ||
      familyName == null ||
      familyName.isEmpty) {
    final Map<String, String> familyInfo = await MockFamily.getFamilyInfo();
    SmartHomeDispatcher.instance.dispatch(FamilyInfoChangedAction(
        familyId: familyInfo['familyId'] ?? '',
        familyName: familyInfo['familyName'] ?? ''));
  }
}
