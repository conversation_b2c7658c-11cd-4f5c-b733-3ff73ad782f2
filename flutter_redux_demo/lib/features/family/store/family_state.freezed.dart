// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'family_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FamilyState {
  String get familyId => throw _privateConstructorUsedError;
  String get familyName => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FamilyStateCopyWith<FamilyState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FamilyStateCopyWith<$Res> {
  factory $FamilyStateCopyWith(
          FamilyState value, $Res Function(FamilyState) then) =
      _$FamilyStateCopyWithImpl<$Res, FamilyState>;
  @useResult
  $Res call({String familyId, String familyName});
}

/// @nodoc
class _$FamilyStateCopyWithImpl<$Res, $Val extends FamilyState>
    implements $FamilyStateCopyWith<$Res> {
  _$FamilyStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? familyId = null,
    Object? familyName = null,
  }) {
    return _then(_value.copyWith(
      familyId: null == familyId
          ? _value.familyId
          : familyId // ignore: cast_nullable_to_non_nullable
              as String,
      familyName: null == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FamilyStateImplCopyWith<$Res>
    implements $FamilyStateCopyWith<$Res> {
  factory _$$FamilyStateImplCopyWith(
          _$FamilyStateImpl value, $Res Function(_$FamilyStateImpl) then) =
      __$$FamilyStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String familyId, String familyName});
}

/// @nodoc
class __$$FamilyStateImplCopyWithImpl<$Res>
    extends _$FamilyStateCopyWithImpl<$Res, _$FamilyStateImpl>
    implements _$$FamilyStateImplCopyWith<$Res> {
  __$$FamilyStateImplCopyWithImpl(
      _$FamilyStateImpl _value, $Res Function(_$FamilyStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? familyId = null,
    Object? familyName = null,
  }) {
    return _then(_$FamilyStateImpl(
      familyId: null == familyId
          ? _value.familyId
          : familyId // ignore: cast_nullable_to_non_nullable
              as String,
      familyName: null == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FamilyStateImpl implements _FamilyState {
  const _$FamilyStateImpl({this.familyId = '', this.familyName = ''});

  @override
  @JsonKey()
  final String familyId;
  @override
  @JsonKey()
  final String familyName;

  @override
  String toString() {
    return 'FamilyState(familyId: $familyId, familyName: $familyName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FamilyStateImpl &&
            (identical(other.familyId, familyId) ||
                other.familyId == familyId) &&
            (identical(other.familyName, familyName) ||
                other.familyName == familyName));
  }

  @override
  int get hashCode => Object.hash(runtimeType, familyId, familyName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FamilyStateImplCopyWith<_$FamilyStateImpl> get copyWith =>
      __$$FamilyStateImplCopyWithImpl<_$FamilyStateImpl>(this, _$identity);
}

abstract class _FamilyState implements FamilyState {
  const factory _FamilyState({final String familyId, final String familyName}) =
      _$FamilyStateImpl;

  @override
  String get familyId;
  @override
  String get familyName;
  @override
  @JsonKey(ignore: true)
  _$$FamilyStateImplCopyWith<_$FamilyStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
