import 'package:redux/redux.dart';
import '../../../smarthome/store/smarthome_state.dart';

class NavigatorSelectors {
  /// 获取导航条显示名称的selector
  static String displayNameSelector(Store<SmartHomeState> store) {
    final bool isLogin = store.state.authState.isLogin;
    final String familyName = store.state.familyState.familyName;

    if (isLogin && familyName.isNotEmpty) {
      return familyName;
    } else {
      return '请登录';
    }
  }
}
