import 'package:flutter_redux_demo/features/auth/store/auth_actions.dart';
import 'package:flutter_redux_demo/smarthome/store/smarthome_state.dart';
import 'package:redux/redux.dart';

// Auth模块的combineReducer
final Reducer<SmartHomeState> authCombineReducer = combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UserLoginStatusChangedAction>(_updateLoginStatus).call,
]);

// 更新登录状态
SmartHomeState _updateLoginStatus(SmartHomeState state, UserLoginStatusChangedAction action) {
  return state.copyWith(
    authState: state.authState.copyWith(isLogin: action.isLogin),
  );
}
