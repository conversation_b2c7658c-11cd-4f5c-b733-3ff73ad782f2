// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'env_devices_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnvDevicesData _$EnvDevicesDataFromJson(Map<String, dynamic> json) =>
    EnvDevicesData(
      spaces: (json['spaces'] as List<dynamic>?)
              ?.map((e) =>
                  SpaceEnvironmentModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$EnvDevicesDataToJson(EnvDevicesData instance) =>
    <String, dynamic>{
      'spaces': instance.spaces,
    };

SpaceEnvironmentModel _$SpaceEnvironmentModelFromJson(
    Map<String, dynamic> json) {
  $checkKeys(
    json,
    requiredKeys: const ['spaceId'],
  );
  return SpaceEnvironmentModel(
    spaceId: json['spaceId'] as String,
    temperature: (json['temperature'] as List<dynamic>?)
            ?.map((e) => EnvDeviceItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        [],
    humidity: (json['humidity'] as List<dynamic>?)
            ?.map((e) => EnvDeviceItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        [],
    pm25: (json['pm25'] as List<dynamic>?)
            ?.map((e) => EnvDeviceItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        [],
  );
}

Map<String, dynamic> _$SpaceEnvironmentModelToJson(
        SpaceEnvironmentModel instance) =>
    <String, dynamic>{
      'spaceId': instance.spaceId,
      'temperature': instance.temperature,
      'humidity': instance.humidity,
      'pm25': instance.pm25,
    };

EnvDeviceItemModel _$EnvDeviceItemModelFromJson(Map<String, dynamic> json) {
  $checkKeys(
    json,
    requiredKeys: const ['deviceId', 'deviceName', 'roomName', 'reportedValue'],
  );
  return EnvDeviceItemModel(
    selected: json['selected'] as bool? ?? false,
    deviceId: json['deviceId'] as String,
    deviceName: json['deviceName'] as String,
    roomName: json['roomName'] as String,
    floorName: json['floorName'] as String? ?? '',
    cardPageImg: json['cardPageImg'] as String? ?? '',
    reportedValue: json['reportedValue'] as String,
    isOnline: json['isOnline'] as bool? ?? false,
  );
}

Map<String, dynamic> _$EnvDeviceItemModelToJson(EnvDeviceItemModel instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'roomName': instance.roomName,
      'floorName': instance.floorName,
      'cardPageImg': instance.cardPageImg,
      'reportedValue': instance.reportedValue,
      'isOnline': instance.isOnline,
    };

TemperatureDevice _$TemperatureDeviceFromJson(Map<String, dynamic> json) {
  $checkKeys(
    json,
    requiredKeys: const ['deviceId', 'deviceName', 'roomName', 'reportedValue'],
  );
  return TemperatureDevice(
    selected: json['selected'] as bool? ?? false,
    deviceId: json['deviceId'] as String,
    deviceName: json['deviceName'] as String,
    roomName: json['roomName'] as String,
    floorName: json['floorName'] as String? ?? '',
    cardPageImg: json['cardPageImg'] as String? ?? '',
    reportedValue: json['reportedValue'] as String,
    isOnline: json['isOnline'] as bool? ?? false,
  );
}

Map<String, dynamic> _$TemperatureDeviceToJson(TemperatureDevice instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'roomName': instance.roomName,
      'floorName': instance.floorName,
      'cardPageImg': instance.cardPageImg,
      'reportedValue': instance.reportedValue,
      'isOnline': instance.isOnline,
    };

HumidityDevice _$HumidityDeviceFromJson(Map<String, dynamic> json) {
  $checkKeys(
    json,
    requiredKeys: const ['deviceId', 'deviceName', 'roomName', 'reportedValue'],
  );
  return HumidityDevice(
    selected: json['selected'] as bool? ?? false,
    deviceId: json['deviceId'] as String,
    deviceName: json['deviceName'] as String,
    roomName: json['roomName'] as String,
    floorName: json['floorName'] as String? ?? '',
    cardPageImg: json['cardPageImg'] as String? ?? '',
    reportedValue: json['reportedValue'] as String,
    isOnline: json['isOnline'] as bool? ?? false,
  );
}

Map<String, dynamic> _$HumidityDeviceToJson(HumidityDevice instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'roomName': instance.roomName,
      'floorName': instance.floorName,
      'cardPageImg': instance.cardPageImg,
      'reportedValue': instance.reportedValue,
      'isOnline': instance.isOnline,
    };

PM25Device _$PM25DeviceFromJson(Map<String, dynamic> json) {
  $checkKeys(
    json,
    requiredKeys: const ['deviceId', 'deviceName', 'roomName', 'reportedValue'],
  );
  return PM25Device(
    selected: json['selected'] as bool? ?? false,
    deviceId: json['deviceId'] as String,
    deviceName: json['deviceName'] as String,
    roomName: json['roomName'] as String,
    floorName: json['floorName'] as String? ?? '',
    cardPageImg: json['cardPageImg'] as String? ?? '',
    reportedValue: json['reportedValue'] as String,
    isOnline: json['isOnline'] as bool? ?? false,
  );
}

Map<String, dynamic> _$PM25DeviceToJson(PM25Device instance) =>
    <String, dynamic>{
      'selected': instance.selected,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'roomName': instance.roomName,
      'floorName': instance.floorName,
      'cardPageImg': instance.cardPageImg,
      'reportedValue': instance.reportedValue,
      'isOnline': instance.isOnline,
    };
