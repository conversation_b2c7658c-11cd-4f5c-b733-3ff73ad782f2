import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/foundation.dart';
import 'package:upservice/model/uhome_response_model.dart';
import 'package:json_annotation/json_annotation.dart';

import '../constants/wholehouse_constants.dart';

part 'env_devices_response_model.g.dart';

/// 环境设备接口响应模型
class EnvDevicesResponseModel extends UhomeResponseModel {
  late final EnvDevicesData environmentData;

  EnvDevicesResponseModel.fromJson(super.data) : super.fromJson() {
    environmentData = EnvDevicesData.fromJson(Map<String, dynamic>.from(super.retData));
  }

  @override
  String toString() =>
      'EnvDevicesResponseModel{environmentData: $environmentData}';
}

/// 环境数据模型
@JsonSerializable()
class EnvDevicesData {
  @JsonKey(defaultValue: <SpaceEnvironmentModel>[])
  final List<SpaceEnvironmentModel> spaces;

  const EnvDevicesData({
    required this.spaces,
  });

  factory EnvDevicesData.fromJson(Map<String, dynamic> json) {
    final List<dynamic> spacesJson = json['spaces'] as List<dynamic>? ?? <dynamic>[];
    
    final List<SpaceEnvironmentModel> spaces = spacesJson
        .whereType<Map<String, dynamic>>()
        .map((Map<String, dynamic> item) => _createSpaceModel(item))
        .toList();

    return EnvDevicesData(spaces: spaces);
  }

  Map<String, dynamic> toJson() => _$EnvDevicesDataToJson(this);

  // 从JSON数据创建空间环境模型
  static SpaceEnvironmentModel _createSpaceModel(Map<String, dynamic> spaceJson) {
    final String spaceId = spaceJson['spaceId'] as String? ?? '';
    
    // 创建临时空间模型用于处理设备
    final List<EnvDeviceItemModel> tempTemperature = <EnvDeviceItemModel>[];
    final List<EnvDeviceItemModel> tempHumidity = <EnvDeviceItemModel>[];
    final List<EnvDeviceItemModel> tempPm25 = <EnvDeviceItemModel>[];
    
    final Map<String, List<dynamic>> devicesByType = _getDevicesByType(spaceJson);
    _processDevicesByType(devicesByType, tempTemperature, tempHumidity, tempPm25);

    return SpaceEnvironmentModel(
      spaceId: spaceId,
      temperature: tempTemperature,
      humidity: tempHumidity,
      pm25: tempPm25,
    );
  }

  // 从JSON获取按类型分组的设备列表
  static Map<String, List<dynamic>> _getDevicesByType(
      Map<String, dynamic> spaceJson) {
    return <String, List<dynamic>>{
      WholehouseConstants.temperatureKey: spaceJson[WholehouseConstants.temperatureKey] as List<dynamic>? ?? <dynamic>[],
      WholehouseConstants.humidityKey: spaceJson[WholehouseConstants.humidityKey] as List<dynamic>? ?? <dynamic>[],
      WholehouseConstants.pm25Key: spaceJson[WholehouseConstants.pm25Key] as List<dynamic>? ?? <dynamic>[],
    };
  }

  // 处理按类型分组的设备并添加到空间模型中
  static void _processDevicesByType(
      Map<String, List<dynamic>> devicesByType, 
      List<EnvDeviceItemModel> temperature,
      List<EnvDeviceItemModel> humidity,
      List<EnvDeviceItemModel> pm25) {
    devicesByType.forEach((String deviceType, List<dynamic> devicesJson) {
      final List<EnvDeviceItemModel> devices = devicesJson
          .whereType<Map<String, dynamic>>()
          .map((Map<String, dynamic> item) => EnvDeviceItemModel.fromJson(item))
          .toList();

      for (final EnvDeviceItemModel device in devices) {
        _assignDeviceToCategory(device, deviceType, temperature, humidity, pm25);
      }
    });
  }

  // 根据设备类型或设备ID将设备分配到相应类别
  static void _assignDeviceToCategory(EnvDeviceItemModel device, String deviceType,
      List<EnvDeviceItemModel> temperature,
      List<EnvDeviceItemModel> humidity,
      List<EnvDeviceItemModel> pm25) {
    switch (deviceType) {
      case WholehouseConstants.temperatureKey:
        temperature.add(device);
        break;
      case WholehouseConstants.humidityKey:
        humidity.add(device);
        break;
      case WholehouseConstants.pm25Key:
        pm25.add(device);
        break;
      default:
        _assignDeviceByIdPattern(device, temperature, humidity, pm25);
    }
  }

  // 根据设备ID特征判断设备类型并分配
  static void _assignDeviceByIdPattern(
      EnvDeviceItemModel device, 
      List<EnvDeviceItemModel> temperature,
      List<EnvDeviceItemModel> humidity,
      List<EnvDeviceItemModel> pm25) {
    final String deviceId = device.deviceId.toLowerCase();

    if (deviceId.contains('temp')) {
      temperature.add(device);
    } else if (deviceId.contains('humid')) {
      humidity.add(device);
    } else if (deviceId.contains('pm25') || deviceId.contains('pm2.5')) {
      pm25.add(device);
    } else {
      temperature.add(device);
    }
  }

  @override
  String toString() {
    final StringBuffer buffer = StringBuffer('EnvDevicesData {\n');
    buffer.write('  spaces: [\n');

    for (int i = 0; i < spaces.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${spaces[i].toString()}');
    }

    buffer.write('\n  ]\n}');
    return buffer.toString();
  }
}

/// 房间环境信息
@JsonSerializable()
class SpaceEnvironmentModel {
  @JsonKey(required: true)
  final String spaceId;
  
  @JsonKey(defaultValue: <EnvDeviceItemModel>[])
  final List<EnvDeviceItemModel> temperature;
  
  @JsonKey(defaultValue: <EnvDeviceItemModel>[])
  final List<EnvDeviceItemModel> humidity;
  
  @JsonKey(defaultValue: <EnvDeviceItemModel>[])
  final List<EnvDeviceItemModel> pm25;

  const SpaceEnvironmentModel({
    required this.spaceId,
    required this.temperature,
    required this.humidity,
    required this.pm25,
  });

  factory SpaceEnvironmentModel.fromJson(Map<String, dynamic> json) {
    return _$SpaceEnvironmentModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SpaceEnvironmentModelToJson(this);

  @override
  String toString() {
    final StringBuffer buffer = StringBuffer('SpaceEnvironmentModel {\n');
    buffer.write('  spaceId: $spaceId,\n');

    // 打印温度设备详情
    buffer.write('  temperature: [\n');
    for (int i = 0; i < temperature.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${temperature[i].toString()}');
    }
    buffer.write('\n  ],\n');

    // 打印湿度设备详情
    buffer.write('  humidity: [\n');
    for (int i = 0; i < humidity.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${humidity[i].toString()}');
    }
    buffer.write('\n  ],\n');

    // 打印PM2.5设备详情
    buffer.write('  pm25: [\n');
    for (int i = 0; i < pm25.length; i++) {
      if (i > 0) buffer.write(',\n');
      buffer.write('    ${pm25[i].toString()}');
    }
    buffer.write('\n  ]\n}');

    return buffer.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SpaceEnvironmentModel &&
        other.spaceId == spaceId &&
        listEquals(other.temperature, temperature) &&
        listEquals(other.humidity, humidity) &&
        listEquals(other.pm25, pm25);
  }

  @override
  int get hashCode => Object.hash(
        spaceId,
        Object.hashAll(temperature),
        Object.hashAll(humidity),
        Object.hashAll(pm25),
      );
}

/// 环境设备数据模型
@JsonSerializable()
class EnvDeviceItemModel {
  @JsonKey(defaultValue: false)
  final bool selected;
  
  @JsonKey(required: true)
  final String deviceId;
  
  @JsonKey(required: true)
  final String deviceName;
  
  @JsonKey(required: true)
  final String roomName;
  
  @JsonKey(defaultValue: '')
  final String floorName;
  
  @JsonKey(defaultValue: '')
  final String cardPageImg;
  
  @JsonKey(required: true)
  final String reportedValue;
  
  @JsonKey(defaultValue: false)
  final bool isOnline;

  const EnvDeviceItemModel({
    required this.selected,
    required this.deviceId,
    required this.deviceName,
    required this.roomName,
    required this.floorName,
    required this.cardPageImg,
    required this.reportedValue,
    required this.isOnline,
  });

  /// 格式化上报值，四舍五入并只保留整数部分
  static String formatReportedValue(String value) {
    final double? doubleValue = double.tryParse(value);

    // 如果解析成功，进行四舍五入并转为整数
    if (doubleValue != null) {
      final int roundedValue = doubleValue.round();
      return roundedValue.toString();
    }

    return value;
  }

  factory EnvDeviceItemModel.fromJson(Map<String, dynamic> json) {
    final String reportedValue = formatReportedValue(json['reportedValue'] as String? ?? '');
    
    return EnvDeviceItemModel(
      selected: json['selected'] as bool? ?? false,
      deviceId: json['deviceId'] as String? ?? '',
      deviceName: json['deviceName'] as String? ?? '',
      roomName: json['roomName'] as String? ?? '',
      floorName: json['floorName'] as String? ?? '',
      cardPageImg: json['cardPageImg'] as String? ?? '',
      reportedValue: reportedValue,
      isOnline: json['isOnline'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() => _$EnvDeviceItemModelToJson(this);

  @override
  String toString() => 'EnvDeviceItemModel{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    reportedValue: $reportedValue,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnvDeviceItemModel &&
        other.deviceId == deviceId &&
        other.deviceName == deviceName &&
        other.roomName == roomName &&
        other.floorName == floorName &&
        other.cardPageImg == cardPageImg &&
        other.reportedValue == reportedValue &&
        other.isOnline == isOnline &&
        other.selected == selected;
  }

  @override
  int get hashCode => Object.hash(
        deviceId,
        deviceName,
        roomName,
        floorName,
        cardPageImg,
        reportedValue,
        isOnline,
        selected,
      );
}

@JsonSerializable()
class TemperatureDevice extends EnvDeviceItemModel {
  const TemperatureDevice({
    required super.selected,
    required super.deviceId,
    required super.deviceName,
    required super.roomName,
    required super.floorName,
    required super.cardPageImg,
    required super.reportedValue,
    required super.isOnline,
  });

  factory TemperatureDevice.fromJson(Map<String, dynamic> json) {
    // 获取并格式化reportedValue
    final String reportedValue = EnvDeviceItemModel.formatReportedValue(
        json['reportedValue'] as String? ?? '');

    return TemperatureDevice(
      selected: json['selected'] as bool? ?? false,
      deviceId: json['deviceId'] as String? ?? '',
      deviceName: json['deviceName'] as String? ?? '',
      roomName: json['roomName'] as String? ?? '',
      floorName: json['floorName'] as String? ?? '',
      cardPageImg: json['cardPageImg'] as String? ?? '',
      reportedValue: reportedValue,
      isOnline: json['isOnline'] as bool? ?? false,
    );
  }

  @override
  Map<String, dynamic> toJson() => _$TemperatureDeviceToJson(this);

  double get temperature => double.tryParse(reportedValue) ?? 0.0;

  @override
  String toString() => 'TemperatureDevice{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    temperature: $reportedValue°C,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';
}

@JsonSerializable()
class HumidityDevice extends EnvDeviceItemModel {
  const HumidityDevice({
    required super.selected,
    required super.deviceId,
    required super.deviceName,
    required super.roomName,
    required super.floorName,
    required super.cardPageImg,
    required super.reportedValue,
    required super.isOnline,
  });

  factory HumidityDevice.fromJson(Map<String, dynamic> json) {
    // 获取并格式化reportedValue
    final String reportedValue = EnvDeviceItemModel.formatReportedValue(
        json['reportedValue'] as String? ?? '');

    return HumidityDevice(
      selected: json['selected'] as bool? ?? false,
      deviceId: json['deviceId'] as String? ?? '',
      deviceName: json['deviceName'] as String? ?? '',
      roomName: json['roomName'] as String? ?? '',
      floorName: json['floorName'] as String? ?? '',
      cardPageImg: json['cardPageImg'] as String? ?? '',
      reportedValue: reportedValue,
      isOnline: json['isOnline'] as bool? ?? false,
    );
  }

  @override
  Map<String, dynamic> toJson() => _$HumidityDeviceToJson(this);

  int get humidity => int.tryParse(reportedValue) ?? 0;

  @override
  String toString() => 'HumidityDevice{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    humidity: $reportedValue%,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';
}

@JsonSerializable()
class PM25Device extends EnvDeviceItemModel {
  const PM25Device({
    required super.selected,
    required super.deviceId,
    required super.deviceName,
    required super.roomName,
    required super.floorName,
    required super.cardPageImg,
    required super.reportedValue,
    required super.isOnline,
  });

  factory PM25Device.fromJson(Map<String, dynamic> json) {
    // 获取并格式化reportedValue
    final String reportedValue = EnvDeviceItemModel.formatReportedValue(
        json['reportedValue'] as String? ?? '');

    return PM25Device(
      selected: json['selected'] as bool? ?? false,
      deviceId: json['deviceId'] as String? ?? '',
      deviceName: json['deviceName'] as String? ?? '',
      roomName: json['roomName'] as String? ?? '',
      floorName: json['floorName'] as String? ?? '',
      cardPageImg: json['cardPageImg'] as String? ?? '',
      reportedValue: reportedValue,
      isOnline: json['isOnline'] as bool? ?? false,
    );
  }

  @override
  Map<String, dynamic> toJson() => _$PM25DeviceToJson(this);

  int get pm25 => int.tryParse(reportedValue) ?? 0;

  @override
  String toString() => 'PM25Device{\n'
      '    deviceId: $deviceId,\n'
      '    deviceName: $deviceName,\n'
      '    roomName: $roomName,\n'
      '    floorName: $floorName,\n'
      '    cardPageImg: $cardPageImg,\n'
      '    pm25: $reportedValue μg/m³,\n'
      '    isOnline: $isOnline,\n'
      '    selected: $selected\n'
      '  }';
}
