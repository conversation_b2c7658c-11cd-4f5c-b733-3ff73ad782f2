import 'package:redux/redux.dart';

import '../../../smarthome/store/smarthome_actions.dart';
import '../../../smarthome/store/smarthome_dispatcher.dart';
import '../../../smarthome/store/smarthome_state.dart';
import 'wholehouse_actions.dart';

class WholeHouseMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    switch (action.runtimeType) {
      case SmartHomeInitializedAction:
        _fetchWholeHouseInfo(store, action);
        break;
      case SmartHomeAppearedAction:
        _fetchWholeHouseInfo(store, action);
        break;
      default:
        next(action);
        break;
    }
    next(action);
  }
}

Future<void> _fetchWholeHouseInfo(
    Store<SmartHomeState> store, dynamic action) async {
  // 请求全屋信息，请求成功后发送WholeHouseInfoChangedAction
  SmartHomeDispatcher.instance
      .dispatch(WholeHouseInfoChangedAction(wholeHouseInfo: ''));
}
