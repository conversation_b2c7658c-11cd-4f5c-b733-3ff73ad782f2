// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wholehouse_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WholeHouseState {
  String get wholeHouseInfo => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WholeHouseStateCopyWith<WholeHouseState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WholeHouseStateCopyWith<$Res> {
  factory $WholeHouseStateCopyWith(
          WholeHouseState value, $Res Function(WholeHouseState) then) =
      _$WholeHouseStateCopyWithImpl<$Res, WholeHouseState>;
  @useResult
  $Res call({String wholeHouseInfo});
}

/// @nodoc
class _$WholeHouseStateCopyWithImpl<$Res, $Val extends WholeHouseState>
    implements $WholeHouseStateCopyWith<$Res> {
  _$WholeHouseStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wholeHouseInfo = null,
  }) {
    return _then(_value.copyWith(
      wholeHouseInfo: null == wholeHouseInfo
          ? _value.wholeHouseInfo
          : wholeHouseInfo // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WholeHouseStateImplCopyWith<$Res>
    implements $WholeHouseStateCopyWith<$Res> {
  factory _$$WholeHouseStateImplCopyWith(_$WholeHouseStateImpl value,
          $Res Function(_$WholeHouseStateImpl) then) =
      __$$WholeHouseStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String wholeHouseInfo});
}

/// @nodoc
class __$$WholeHouseStateImplCopyWithImpl<$Res>
    extends _$WholeHouseStateCopyWithImpl<$Res, _$WholeHouseStateImpl>
    implements _$$WholeHouseStateImplCopyWith<$Res> {
  __$$WholeHouseStateImplCopyWithImpl(
      _$WholeHouseStateImpl _value, $Res Function(_$WholeHouseStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wholeHouseInfo = null,
  }) {
    return _then(_$WholeHouseStateImpl(
      wholeHouseInfo: null == wholeHouseInfo
          ? _value.wholeHouseInfo
          : wholeHouseInfo // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$WholeHouseStateImpl implements _WholeHouseState {
  const _$WholeHouseStateImpl({this.wholeHouseInfo = ''});

  @override
  @JsonKey()
  final String wholeHouseInfo;

  @override
  String toString() {
    return 'WholeHouseState(wholeHouseInfo: $wholeHouseInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WholeHouseStateImpl &&
            (identical(other.wholeHouseInfo, wholeHouseInfo) ||
                other.wholeHouseInfo == wholeHouseInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, wholeHouseInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WholeHouseStateImplCopyWith<_$WholeHouseStateImpl> get copyWith =>
      __$$WholeHouseStateImplCopyWithImpl<_$WholeHouseStateImpl>(
          this, _$identity);
}

abstract class _WholeHouseState implements WholeHouseState {
  const factory _WholeHouseState({final String wholeHouseInfo}) =
      _$WholeHouseStateImpl;

  @override
  String get wholeHouseInfo;
  @override
  @JsonKey(ignore: true)
  _$$WholeHouseStateImplCopyWith<_$WholeHouseStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
