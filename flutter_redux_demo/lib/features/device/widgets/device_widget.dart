import 'package:flutter/material.dart';

import '../utils/device_widgets_utils.dart';

class DeviceWidget extends StatelessWidget {
  const DeviceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Si<PERSON>B<PERSON>(
        height: 50,
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            // 这里可以添加按钮点击事件
            DeviceWidgetsUtils.deviceControlBtnPressed(context);
          },
          child: const Text('设备控制'),
        ),
      ),
    );
  }
}
