import 'package:flutter/material.dart';

class DeviceWidgetsUtils {
  static Future<void> deviceControlBtnPressed(BuildContext context) async {
    //模拟UI上点击属性控制一类的，无需更新数据
    return Future<void>.delayed(const Duration(seconds: 2), () {
      showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return const AlertDialog(
            title: Text('设备控制成功！'),
          );
        },
      );
    });
  }
}
