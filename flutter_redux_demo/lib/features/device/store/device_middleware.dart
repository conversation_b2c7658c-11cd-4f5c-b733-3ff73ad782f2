import 'package:device_utils/log/log.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:redux/redux.dart';

import '../../../common/constants/smarthome_constant.dart';
import '../../../smarthome/store/smarthome_actions.dart';
import '../../../smarthome/store/smarthome_dispatcher.dart';
import '../../../smarthome/store/smarthome_state.dart';
import 'device_actions.dart';

class DeviceMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    switch (action.runtimeType) {
      case SmartHomeInitializedAction:
        _initilizePlugins(store, action);
        break;
      default:
        next(action);
        break;
    }
    next(action);
  }
}

Future<void> _initilizePlugins(
    Store<SmartHomeState> store, dynamic action) async {
  UpDevicePlugin.subscribeDeviceListChange(_deviceListChangeCallback)
      .catchError((dynamic error) {
    DevLogger.error(
        tag: SmartHomeConstant.package,
        msg: 'UpDevicePlugin.subscribeDeviceListChange error $error');
  });
}

void _deviceListChangeCallback() {
  // 处理设备列表变化
  // 请求设备列表，请求成功后发送DeviceListChangedAction
  SmartHomeDispatcher.instance
      .dispatch(DeviceListChangedAction(deviceList: <dynamic>[]));
}
