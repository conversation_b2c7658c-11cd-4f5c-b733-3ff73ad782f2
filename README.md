# Code Review 项目

这是一个完整的代码评审系统，包含：

1. 代码评审系统
2. Flutter Redux 示例项目
3. Flutter Redux 使用规范

## 📁 项目结构

```text
code-review/
├── README.md                           # 项目说明文档
├── flutter-redux-standards.md          # Flutter Redux 使用规范
├── flutter_redux_demo/                 # Flutter Redux 示例项目
└── code-review/                        # 代码评审系统
    ├── code-review-guideline.md        # 代码评审指南
    └── feedback/                       # 历史反馈数据库
        ├── database/
        │   ├── correct-reviews.md     # 正确的评审反馈
        │   └── incorrect-reviews.md   # 错误的评审反馈
        └── feedback_processor.py      # 反馈处理脚本
```

## 🎯 项目目标

- 提供智能化的代码评审指导
- 建立 Flutter Redux 最佳实践标准
- 通过历史反馈持续改进评审质量
- 确保代码质量和架构一致性

---

## 1. 📋 代码评审指南使用说明

### 概述

`code-review-guideline.md` 是一个智能化的代码评审指南，基于《重构 改善既有代码的设计》原则，专门为 Flutter 和 Redux 项目设计。

### 主要功能

- **智能评审流程**：自动获取 GitLab MR 变更信息
- **全面规范检查**：涵盖代码风格、架构设计、性能优化等
- **Redux 专项检查**：针对 Redux 架构的专门评审清单
- **历史反馈学习**：基于历史评审反馈提高准确性

### 使用方法

#### 触发评审

```bash
# 方式1：完整命令
review https://git.haier.net/uplus/flutter/packages/smart_home/-/merge_requests/3154

# 方式2：简写命令
re https://git.haier.net/uplus/flutter/packages/smart_home/-/merge_requests/3154
```

---

### 🔄 反馈机制

#### 反馈命令

```bash
# 正确评审反馈
feedback correct "CR-20250519-a1b2c3" "评价内容"

# 错误评审反馈
feedback incorrect "CR-20250519-a1b2c3" "错误原因" "正确评审意见"

# 批量反馈
feedback batch --commands '命令1' '命令2'
```

#### 反馈处理

- 系统自动调用 `feedback_processor.py` 处理反馈
- 反馈内容更新到 `feedback/database/` 目录
- 用于持续改进评审质量

---

## 2. 🚀 Flutter Redux Demo 说明

### 概述

`flutter_redux_demo/` 是一个完整的 Flutter Redux 示例项目，展示了 Redux 架构的最佳实践实现。demo中的示例完全符合`flutter-redux-standards.md`中的Flutter Redux使用规范。

### 目录结构

```text

lib/
├── main.dart                    # 应用入口
├── app.dart                     # 根组件
├── smarthome/                   # 应用级别配置
│   ├── smarthome.dart          # 主页面
│   └── store/                  # Redux相关
│       ├── smarthome_actions.dart
│       ├── smarthome_reducer.dart
│       ├── smarthome_state.dart
│       ├── smarthome_middleware.dart
│       ├── smarthome_dispatcher.dart
│       └── smarthome_state.freezed.dart
├── common/                      # 通用模块
│   ├── constants/              # 常量定义
│   ├── utils/                  # 工具方法
│   ├── widgets/                # 通用组件
│   └── services/              # 通用服务
├── features/                   # 功能模块
│   ├── auth/                  # 认证功能
│   ├── device/                # 设备功能
│   ├── family/                # 家庭功能
│   ├── wholehouse/            # 全屋功能
│   └── navigator/             # 导航功能
└── Mock/                      # Mock数据

```

### 运行项目

```bash
cd flutter_redux_demo
flutter pub get
dart run build_runner build
flutter run -d macos
```

---

## 3. 📖 Flutter Redux 使用规范说明

### 概述

`flutter-redux-standards.md` 是 Flutter Redux 项目的完整使用规范，基于实际项目经验总结的最佳实践。

### 规范内容

#### 核心规范

1. **Action 设计规范**
   - 事件风格命名：`UserLoginStatusChangedAction`
   - 继承 BaseAction 基类
   - 使用命名参数
   - 只包含可序列化数据

2. **Reducer 编写规范**
   - 使用 Freezed 确保不可变性
   - 纯函数实现，无副作用
   - 使用 copyWith 方法创建新状态

3. **Middleware 使用规范**
   - 处理所有副作用（API调用、异步操作）
   - 使用 async/await 处理异步操作
   - 通过 dispatch Action 表达状态修改意图

4. **State 设计规范**
   - 使用 Freezed 生成不可变代码
   - 按模块组织状态结构
   - 避免状态嵌套过深

5. **Selectors 使用规范**
   - 创建专门的 Selectors 类（如 `NavigatorSelectors`）
   - 封装数据选择和转换逻辑
   - 提供计算属性和性能优化
   - 使用 `Selector` 后缀命名方法

#### 代码生成

```bash
# 生成 Freezed 代码
dart run build_runner build

# 监听模式（开发时）
dart run build_runner watch
```

---

## 🚀 快速开始

1. **了解评审指南**：阅读 `code-review-guideline.md`
2. **学习最佳实践**：查看 `flutter_redux_demo/` 示例
3. **掌握使用规范**：参考 `flutter-redux-standards.md`
4. **开始代码评审**：使用 `re {mr_url}` 命令

---

## 📋 Changelog

### 2025-07-16

- **新增** flutter_redux_demo
  - 完整的Redux调用示例
- **更新** flutter-redux-standards
  - 更新Middleware、Action命名、WidgetUtils、Service部分的示例
- **更新** code-review-guideline
  - 根据demo和standards，同步更新guideline

### 2025-07-03

- **更新** Redux使用规范
  - 完善Redux架构图
  - 更新Redux核心模块使用规范

### 2025-06-13

- **新增** 完整的代码评审系统
  - 智能代码评审指南 (`code-review-guideline.md`)
  - 历史反馈数据库 (`feedback/database/`)
  - 反馈处理脚本 (`feedback_processor.py`)

---
